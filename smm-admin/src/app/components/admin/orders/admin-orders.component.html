<div class="layout-container py-6 px-4">
  <!-- <h1 class="text-xl font-bold mb-2">T<PERSON><PERSON> kiếm đơn hàng</h1> -->

  <h2>{{ 'admin.orders.search_orders' | translate }}</h2>

  <!-- Status filter tabs -->
  <div class="mb-6 ">
    <div class="flex flex-wrap -mb-px">
      <div *ngFor="let filter of statusFilters" class="mr-2">
        <button (click)="toggleFilter(filter)"
          [ngClass]="{'text-[var(--primary)] border-b-2 !border-[var(--primary)]' : filter.active, 'text-gray-500 hover:border-b-2  hover:text-gray-700 hover:border-gray-300': !filter.active}"
          class="inline-block p-4 border-transparent rounded-t-lg ">
          {{ filter.label }}
        </button>
      </div>

      <div class="ml-auto flex items-center">
        <button (click)="toggleFilters()" class="inline-flex items-center text-[var(--primary)]">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          {{ showFilters ? ('admin.orders.hide_filters' | translate) : ('admin.orders.show_filters' | translate) }}
        </button>
      </div>
    </div>
  </div>

  <!-- Search bar -->
  <div class="mb-6 flex flex-row w-full gap-4 items-stretch h-[46px]">
    <div class="flex-grow h-full">
      <app-search-box placeholder="{{ 'admin.orders.search_placeholder' | translate }}" buttonText="{{ 'search' | translate }}" buttonPosition="right"
        containerClass="rounded-lg w-full h-full" inputClass="bg-white text-gray-700 w-full h-full"
        buttonClass="bg-[var(--primary)] text-white font-medium "
        (searchEvent)="onSearch($event)"></app-search-box>
    </div>
    <!-- <div>
      <button (click)="toggleFilters()"
        class="px-5  h-full text-sm font-medium text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 flex items-center flex-shrink-0">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        {{ 'admin.orders.hide_filters' | translate }}
      </button>
    </div> -->
  </div>

  <!-- Advanced filters -->
  <div *ngIf="showFilters" class="mb-6 p-4 bg-white border border-gray-200 rounded-lg ">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Category dropdown -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'admin.orders.category' | translate }}</label>
        <app-icon-dropdown [options]="categories" [customClassDropdown]="'bg-white rounded-lg border'"
          [customClassButton]="' h-[46px]'" (selected)="onCategorySelected($event)"></app-icon-dropdown>
      </div>

      <!-- Service dropdown -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'admin.orders.service' | translate }}</label>
        <app-service-dropdown [lite]="true" [options]="services" [customClassDropdown]="'bg-white rounded-lg border '"
          [customClassButton]="' min-h-[46px]'" (selected)="onServiceSelected($event)"></app-service-dropdown>
      </div>

      <!-- Date range picker -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'admin.orders.date' | translate }}</label>

        <app-date-range-picker [containerClass]="'h-[46px]'" [(ngModel)]="dateRange" (dateRangeChanged)="onDateRangeChange($event)"></app-date-range-picker>
      </div>
    </div>

    <!-- User ID filter -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- User ID input -->
      <div>
        <label class="block mb-2 text-sm font-medium text-gray-900">{{ 'admin.orders.user_id' | translate }}</label>
        <div class="flex">
          <input
            type="number"
            [(ngModel)]="userIdInput"
            placeholder="{{ 'admin.orders.enter_user_id' | translate }}"
            class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 h-[46px]"
          >
        </div>
      </div>
    </div>

    <div class="flex justify-between">
      <button (click)="applyFilters()"
        class="px-5 py-2.5 text-sm font-medium text-white bg-[var(--primary)] rounded-lg  focus:ring-4 ">
        {{ 'admin.orders.apply' | translate }}
      </button>

      <button (click)="resetFilters()"
        class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {{ 'admin.orders.reset_filter' | translate }}
      </button>
    </div>
  </div>

  <!-- View toggle buttons (hidden on mobile) -->
  <!-- <div class="flex justify-end mb-4 viewMode-toggle">
    <div class="inline-flex rounded-md shadow-sm" role="group">
      <button type="button"
              (click)="viewMode = 'table'"
              [ngClass]="viewMode === 'table' ? 'bg-[var(--primary)] text-white' : 'bg-white text-gray-700 hover:bg-gray-100'"
              class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-l-lg focus:z-10 focus:ring-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18M3 18h18M3 6h18" />
        </svg>
        {{ 'table_view' | translate }}
      </button>
      <button type="button"
              (click)="viewMode = 'card'"
              [ngClass]="viewMode === 'card' ? 'bg-[var(--primary)] text-white' : 'bg-white text-gray-700 hover:bg-gray-100'"
              class="px-4 py-2 text-sm font-medium border border-gray-200 rounded-r-lg focus:z-10 focus:ring-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
        {{ 'card_view' | translate }}
      </button>
    </div>
  </div> -->

  <!-- Order actions -->
  <div class="overflow-x-auto w-full relative">
      <!-- Loading overlay -->
        <app-global-loading></app-global-loading>

      <!-- Table View -->
      <div *ngIf="viewMode === 'table'" class="min-w-full">

        <table class=" border border-gray-200 w-full">
          <thead class="text-xs text-gray-700  bg-gray-50">
            <tr class="">
              <!-- <th colspan="8">


                  <div class="header-left ">
                    <div class="flex items-center gap-2 flex-wrap">
                      <input id="default-checkbox" type="checkbox" [(ngModel)]="selectAll" (change)="toggleSelectAll()"
                        class="checkbox-label">
                      <span class="ms-2 text-sm font-medium ">{{ 'all_orders' | translate }}</span>
                      <button class="bg-[var(--primary)] text-white px-4 py-2 rounded-lg" (click)="CopyID()">{{ 'copy_id' |
                        translate }}</button>
                      <button class="bg-[#6367FE] text-white px-4 py-2 rounded-lg">{{ 'refill' | translate }}</button>
                    </div>
                  </div>

              </th> -->


            </tr>
            <tr>
              <th scope="col" class="p-4">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="selectAll" (change)="toggleSelectAll()"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                </div>
              </th>
              <th scope="col" class="px-6 py-3">
                <div class="flex items-center justify-between">


                  <!-- Bulk action button - only shown when orders are selected -->
                  <div *ngIf="selectedOrders.length > 0; else noBulkActions" class="relative">
                    <div class="flex items-center">
                      <app-admin-menu
                        [menuItems]="getBulkActionMenuItems()"
                        (menuItemClicked)="onBulkActionMenuItemClick($event)">
                        <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                        <span class="ml-1 text-xs text-blue-600 font-medium">({{ selectedOrders.length }})</span>
                      </app-admin-menu>
                    </div>
                  </div>
                  <ng-template #noBulkActions>
                    <span>{{ 'admin.orders.serial_number' | translate }}</span>
                  </ng-template>
                </div>
              </th>
              <th scope="col" class="px-6 py-3">{{ 'admin.orders.user' | translate }}</th>
              <th scope="col" class="px-6 py-3">{{ 'admin.orders.link' | translate }}</th>
              <th scope="col" class="px-6 py-3">{{ 'admin.orders.remaining' | translate }}</th>
              <th scope="col" class="px-6 py-3">{{ 'admin.orders.status' | translate }}</th>
              <th scope="col" class="px-6 py-3">{{ 'admin.orders.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let order of orders; let i = index" class="bg-white border-b hover:bg-gray-50">
              <td class="w-4 p-4">
                <div class="flex items-center">
                  <input type="checkbox" [checked]="isOrderSelected(order.id)" (change)="toggleOrderSelection(order.id)"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                </div>
              </td>
              <td class="w-4 px-3 py-2">{{ pagination.pageNumber * pagination.pageSize + i + 1 }}</td>
              <td class="px-3 py-2">
                <a [routerLink]="['/panel/users']" [queryParams]="{search: order.user.user_name}" class="text-blue-600 hover:underline">
                  {{ order.user.user_name }}
                </a>
              </td>
              <td class="px-3 py-2">
                <div class="link-content">
                  <div class="text-gray-500 font-medium text-[14px] flex items-center gap-2">
                    ID: {{order.id}} | {{order.created_at | timezone:'short'}}
                    <span class="relative ml-2">
                      <span [ngClass]="{'text-blue-600': order.api_provider, 'text-gray-600': !order.api_provider}"
                            class="cursor-pointer"
                            (mouseenter)="order.api_provider && showTooltip(order.id, $event)"
                            (mouseleave)="hideTooltip()">
                        | {{ order.api_provider ? order.api_provider.name : ('admin.orders.manual' | translate) }}
                      </span>
                      <!-- Tooltip that appears on hover -->
                      <div *ngIf="order.api_provider && activeTooltip === order.id"
                           class="fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-xl p-3 w-64 text-sm pointer-events-auto"
                           [style.left.px]="tooltipPosition.x"
                           [style.top.px]="tooltipPosition.y"
                           [style.position]="'fixed'"
                           [style.transform]="'translate3d(0, 0, 0)'"
                           (mouseenter)="keepTooltipOpen($event)"
                           (mouseleave)="hideTooltip()">
                        <div class="mb-2">
                          <span class="text-gray-500">{{ 'admin.orders.provider_url' | translate }}:</span>
                          <span class="font-medium ml-1 break-all">{{ order.api_provider.url }}</span>
                        </div>
                        <div class="flex items-center">
                          <span class="text-gray-500">{{ 'admin.orders.api_order_id' | translate }}:</span>
                          <span class="font-medium ml-1">{{ order.api_order_id || 'N/A' }}</span>
                          <button *ngIf="order.api_order_id"
                                  (click)="copyToClipboard(order.api_order_id); $event.stopPropagation()"
                                  class="ml-2 text-blue-600 hover:text-blue-800"
                                  title="{{ 'admin.orders.copy_to_clipboard' | translate }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                          </button>
                        </div>

                        <div class="flex items-center">
                          <span class="text-gray-500">{{ 'admin.orders.api_service_id' | translate }}:</span>
                          <span class="font-medium ml-1">{{ order.api_service_id || 'N/A' }} | {{ order.service.original_price }}$</span>
                          <button *ngIf="order.api_service_id"
                                  (click)="copyToClipboard(order.api_service_id); $event.stopPropagation()"
                                  class="ml-2 text-blue-600 hover:text-blue-800"
                                  title="{{ 'admin.orders.copy_to_clipboard' | translate }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </span>
                    <!-- OrderTag display -->
                    <span *ngIf="order.tag"
                          [ngClass]="{
                            'bg-yellow-100 text-yellow-800': order.tag.toLowerCase() === 'refill',
                            'bg-red-100 text-red-800': order.tag.toLowerCase() === 'cancel',
                            'bg-blue-100 text-blue-800': order.tag.toLowerCase() !== 'refill' && order.tag.toLowerCase() !== 'cancel'
                          }"
                          class="px-2 py-1 rounded text-xs font-medium">Request_{{order.tag | lowercase}}</span>
                    <!-- Note icon with tooltip -->
                    <span *ngIf="order.note" class="relative ml-2">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           class="h-4 w-4 text-gray-600 cursor-pointer hover:text-blue-600"
                           fill="none"
                           viewBox="0 0 24 24"
                           stroke="currentColor"
                           (mouseenter)="showNoteTooltip(order.id, $event, order.note)"
                           (mouseleave)="hideNoteTooltip()"
                           title="{{ 'admin.orders.customer_note' | translate }}">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      <!-- Note tooltip -->
                      <div *ngIf="activeNoteTooltip === order.id"
                           class="fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-xl p-3 w-64 text-sm pointer-events-auto"
                           [style.left.px]="noteTooltipPosition.x"
                           [style.top.px]="noteTooltipPosition.y"
                           [style.position]="'fixed'"
                           [style.transform]="'translate3d(0, 0, 0)'"
                           (mouseenter)="keepNoteTooltipOpen($event)"
                           (mouseleave)="hideNoteTooltip()">
                        <div class="mb-1">
                          <span class="text-gray-500 font-medium">{{ 'admin.orders.customer_note' | translate }}:</span>
                        </div>
                        <div class="text-gray-800 break-words">{{ order.note }}</div>
                      </div>
                    </span>
                  </div>
                  <div class="description-row text-[13px] font-semibold">
                    <div class="tag">{{order.service.id}}</div>
                    <span class="">{{order.service.name}} - <span class="text-red-500">${{formatBalance(order.service.price)}} </span></span>
                  </div>
                  <a [href]="order.link" target="_blank" class="link text-[13px] font-semibold">{{order.link}}</a>
                </div>
              </td>
              <td class="px-3 py-2">
                <div class="flex flex-col text-sm">
                  <div class="flex gap-1">
                    <span class="text-gray-500">{{ 'admin.orders.amount' | translate }}:</span>
                    <span class="text-red-500 font-medium">${{ formatBalance(order.actual_charge) }} </span>
                  </div>
                  <div class="flex gap-1">
                    <span class="text-gray-500">{{ 'admin.orders.quantity' | translate }}:</span>
                    <span class="font-medium">{{ order.quantity }}</span>
                  </div>
                  <div class="flex gap-1">
                    <span class="text-gray-500">{{ 'admin.orders.started' | translate }}:</span>
                    <span class="font-medium">{{ order.start_count || 0 }}</span>
                  </div>
                  <div class="flex gap-1">
                    <span class="text-gray-500">{{ 'admin.orders.remaining_quantity' | translate }}:</span>
                    <span class="font-medium">{{ order.remains  }}</span>
                  </div>
                </div>
              </td>
              <td class="px-3 py-2">
                <span [ngClass]="'px-2 py-1 rounded-md text-xs font-medium ' + getStatusClass(order.status)">
                  {{ order.status | titlecase }}
                </span>
              </td>
              <td class="px-3 py-2">
                <div class="relative">
                  <app-admin-menu
                    [menuItems]="getOrderMenuItems(order)"
                    (menuItemClicked)="onOrderMenuItemClick($event, order)">
                    <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                  </app-admin-menu>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Card View (for mobile) -->
      <div *ngIf="viewMode === 'card'" class="grid grid-cols-1 gap-4">
        <!-- Card view header with Select All and bulk actions -->
        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-2">
          <div class="flex justify-between items-center">
            <div class="flex items-center ">
              <input type="checkbox" [checked]="selectAll" (change)="toggleSelectAll()"
                class="w-4 h-4 !mr-2 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
              <span class="text-gray-700 font-medium">{{ 'admin.orders.all_orders' | translate }}</span>
            </div>

            <!-- Bulk action button - only shown when orders are selected -->
            <div *ngIf="selectedOrders.length > 0" class="relative">
              <!-- Bulk action menu using app-admin-menu component -->
              <app-admin-menu
                [menuItems]="getBulkActionMenuItems()"
                (menuItemClicked)="onBulkActionMenuItemClick($event)">
                <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                <span class="ml-1 text-xs text-blue-600 font-medium">({{ selectedOrders.length }})</span>
              </app-admin-menu>
            </div>
          </div>
        </div>

        <!-- Individual order cards -->
        <div *ngFor="let order of orders; let i = index" class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <!-- Card header with checkbox and ID -->
          <div class="flex justify-between items-start mb-3">
            <div class="flex items-center">
              <input type="checkbox" [checked]="isOrderSelected(order.id)" (change)="toggleOrderSelection(order.id)"
                class="w-4 h-4 !mr-2 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
              <div class="text-gray-500 font-medium">
                <div class="flex items-center gap-2 mb-1">
                  <span>ID: {{order.id}}</span>
                  <span class="relative">
                    <span [ngClass]="{'text-blue-600': order.api_provider, 'text-gray-600': !order.api_provider}"
                          class="cursor-pointer"
                          (mouseenter)="order.api_provider && showTooltip(order.id, $event)"
                          (mouseleave)="hideTooltip()">
                      | {{ order.api_provider ? order.api_provider.name : 'Manual' }}
                    </span>
                    <!-- Tooltip that appears on hover -->
                    <div *ngIf="order.api_provider && activeTooltip === order.id"
                         class="fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-xl p-3 w-64 text-sm pointer-events-auto"
                         [style.left.px]="tooltipPosition.x"
                         [style.top.px]="tooltipPosition.y"
                         [style.position]="'fixed'"
                         [style.transform]="'translate3d(0, 0, 0)'"
                         (mouseenter)="keepTooltipOpen($event)"
                         (mouseleave)="hideTooltip()">
                      <div class="mb-2">
                        <span class="text-gray-500">Provider URL:</span>
                        <span class="font-medium ml-1 break-all">{{ order.api_provider.url }}</span>
                      </div>
                      <div class="flex items-center">
                        <span class="text-gray-500">API Order ID:</span>
                        <span class="font-medium ml-1">{{ order.api_order_id || 'N/A' }}</span>
                        <button *ngIf="order.api_order_id"
                                (click)="copyToClipboard(order.api_order_id); $event.stopPropagation()"
                                class="ml-2 text-blue-600 hover:text-blue-800"
                                title="Copy to clipboard">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </span>
                </div>
                <!-- OrderTag and Note icon row -->
                <div class="flex items-center gap-2">
                  <!-- OrderTag display -->
                  <span *ngIf="order.tag"
                        [ngClass]="{
                          'bg-yellow-100 text-yellow-800': order.tag.toLowerCase() === 'refill',
                          'bg-red-100 text-red-800': order.tag.toLowerCase() === 'cancel',
                          'bg-blue-100 text-blue-800': order.tag.toLowerCase() !== 'refill' && order.tag.toLowerCase() !== 'cancel'
                        }"
                        class="px-2 py-1 rounded text-xs font-medium">Request_{{order.tag | lowercase}}</span>
                  <!-- Copy button -->
                  <button (click)="copySingleOrder(order)"
                          class="bg-[var(--primary)] text-white px-2 py-1 rounded text-xs font-medium hover:bg-cyan-600"
                          title="{{ 'admin.orders.copy' | translate }}">
                    {{ 'admin.orders.copy' | translate }}
                  </button>
                  <!-- Note icon with tooltip -->
                  <span *ngIf="order.note" class="relative">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-4 w-4 text-gray-600 cursor-pointer hover:text-blue-600"
                         fill="none"
                         viewBox="0 0 24 24"
                         stroke="currentColor"
                         (mouseenter)="showNoteTooltip(order.id, $event, order.note)"
                         (mouseleave)="hideNoteTooltip()"
                         title="Customer note">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <!-- Note tooltip -->
                    <div *ngIf="activeNoteTooltip === order.id"
                         class="fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-xl p-3 w-64 text-sm pointer-events-auto"
                         [style.left.px]="noteTooltipPosition.x"
                         [style.top.px]="noteTooltipPosition.y"
                         [style.position]="'fixed'"
                         [style.transform]="'translate3d(0, 0, 0)'"
                         (mouseenter)="keepNoteTooltipOpen($event)"
                         (mouseleave)="hideNoteTooltip()">
                      <div class="mb-1">
                        <span class="text-gray-500 font-medium">Customer Note:</span>
                      </div>
                      <div class="text-gray-800 break-words">{{ order.note }}</div>
                    </div>
                  </span>
                </div>
              </div>
            </div>
            <span [ngClass]="'px-2 py-1 rounded-md text-xs font-medium ' + getStatusClass(order.status)">
              {{ order.status | titlecase }}
            </span>
          </div>



          <!-- User info -->
          <div class="mb-3">
            <a [routerLink]="['/panel/users']" [queryParams]="{search: order.user.user_name}" class="text-blue-600 hover:underline font-medium">
              {{ order.user.user_name }}
            </a>
            <div class="text-gray-500 text-sm">{{order.created_at | timezone:'short'}}</div>
          </div>

          <!-- Service info -->
          <div class="mb-3">
            <div class="font-semibold text-[15px] mb-1">
              <span class="tag text-xs bg-gray-100 px-1 py-0.5 rounded mr-1">{{order.service.id}}</span>
              {{order.service.name}}
            </div>
            <div class="text-red-500 font-medium text-sm">${{formatBalance(order.charge)}} <span *ngIf="order.actual_charge && order.actual_charge !== order.charge">({{formatBalance(order.actual_charge)}})</span></div>
          </div>

          <!-- Link -->
          <div class="mb-3 bg-gray-50 p-2 rounded text-sm break-words">
            {{order.link}}
          </div>

          <!-- Order details -->
          <div class="grid grid-cols-2 gap-2 mb-3 text-sm">
            <div>
              <span class="text-gray-500">{{ 'admin.orders.quantity' | translate }}:</span>
              <span class="font-medium ml-1">{{ order.quantity }}</span>
            </div>
            <div>
              <span class="text-gray-500">{{ 'admin.orders.started' | translate }}:</span>
              <span class="font-medium ml-1">{{ order.start_count || 0 }}</span>
            </div>
            <div>
              <span class="text-gray-500">{{ 'admin.orders.remaining_quantity' | translate }}:</span>
              <span class="font-medium ml-1">{{ order.remains  }}</span>
            </div>
            <div>
              <span class="text-gray-500">{{ 'admin.orders.api_order_id' | translate }}:</span>
              <span class="font-medium ml-1">{{ order.api_order_id || '-' }}</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end mt-3">
            <!-- Action menu button -->
            <div class="relative menu-container">
              <!-- Action Menu using app-admin-menu component -->
              <app-admin-menu
                [menuItems]="getOrderMenuItems(order)"
                [isOpen]="activeActionMenu === order.id"
                (menuItemClicked)="onOrderMenuItemClick($event, order)"
                (menuClosed)="activeActionMenu = null">
                <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
              </app-admin-menu>
            </div>
          </div>
        </div>
      </div>
    <!-- </div> -->

  </div>

  <div class="pagination" *ngIf="pagination.totalPages > 0">
    <div class="page-numbers">
        <button class="page-nav" [disabled]="pagination.pageNumber === 0" (click)="goToPage(pagination.pageNumber - 1)">
            <img src="assets/images/chevron.png" alt="prev"/>
        </button>

        <!-- First page (always visible) -->
        <button class="page-num" [ngClass]="{'active': pagination.pageNumber === 0}" (click)="goToPage(0)">1</button>

        <!-- Ellipsis for gap at the beginning (hidden on mobile if not needed) -->
        <button *ngIf="pagination.pageNumber > 3" class="page-num hidden md:block">...</button>

        <!-- Pages before current (limited on mobile) -->
        <ng-container *ngFor="let page of getPageRange()">
            <button *ngIf="page !== 0 && page !== pagination.totalPages - 1"
                    class="page-num"
                    [ngClass]="{
                      'active': pagination.pageNumber === page,
                      'hidden sm:block': page !== pagination.pageNumber && page !== pagination.pageNumber - 1 && page !== pagination.pageNumber + 1
                    }"
                    (click)="goToPage(page)">
                {{ page + 1 }}
            </button>
        </ng-container>

        <!-- Ellipsis for gap at the end (hidden on mobile if not needed) -->
        <button *ngIf="pagination.pageNumber < pagination.totalPages - 4" class="page-num hidden md:block">...</button>

        <!-- Last page (if more than one page) -->
        <button *ngIf="pagination.totalPages > 1"
                class="page-num"
                [ngClass]="{'active': pagination.pageNumber === pagination.totalPages - 1}"
                (click)="goToPage(pagination.totalPages - 1)">
            {{ pagination.totalPages }}
        </button>

        <button class="page-nav" [disabled]="pagination.pageNumber >= pagination.totalPages - 1" (click)="goToPage(pagination.pageNumber + 1)">
            <img src="assets/images/chevron-2.png" alt="next"/>
        </button>
    </div>
    <div class="page-info">
        <span>{{ 'showing' | translate }} {{ pagination.pageNumber * pagination.pageSize + 1 }} -
              {{ (pagination.pageNumber + 1) * pagination.pageSize > pagination.totalElements ?
                 pagination.totalElements :
                 (pagination.pageNumber + 1) * pagination.pageSize }}
              {{ 'of' | translate }} {{ pagination.totalElements }} {{ 'entries' | translate }}</span>
        <div class="show-entries">
            <select [(ngModel)]="pagination.pageSize" (change)="changePageSize()" class="w-16 p-1 border border-gray-300 rounded">
                <option [value]="10">10</option>
                <option [value]="25">25</option>
                <option [value]="50">50</option>
                <option [value]="100">100</option>
            </select>
            <span class="ml-2">{{ 'entries_per_page' | translate }}</span>
        </div>
    </div>
</div>
</div>

<!-- Edit Link Popup -->
<app-edit-link
  *ngIf="showEditLinkModal"
  [link]="selectedOrderLink"
  [orderId]="selectedOrderId || 0"
  (close)="closeEditLinkModal()"
  (linkUpdated)="onLinkUpdated($event)">
</app-edit-link>

<!-- Set Count Popup -->
<app-set-count
  *ngIf="showSetCountModal"
  [startCount]="selectedOrderStartCount"
  [orderId]="selectedOrderId || 0"
  (close)="closeSetCountModal()"
  (countUpdated)="onCountUpdated($event)">
</app-set-count>

<!-- Set Partial Popup -->
<app-set-partial
  *ngIf="showSetPartialModal"
  [orderId]="selectedOrderId!"
  [quantity]="selectedOrderQuantity"
  (close)="closeSetPartialModal()"
  (partialUpdated)="onPartialUpdated($event)">
</app-set-partial>


